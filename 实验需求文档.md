数据库应用系统开发指导书
实验1：需求分析------大学生选课管理系统
1．实验内容说明：
教务处的管理人员录入全校的课程基本信息和本学期的课程授课教师、地点、时间；（教务处课程录入）
在学生入学的时候，学院的管理人员录入学生基本信息；（学院学生信息录入）
学生每学期自己上网登录系统选课，选课成功后信息存入数据库中，学生自己可以查询选课的情况；（学生选课录入、学生选课查询）
学生选课不成功的情况有：
所选课程的先修课还没有记录，系统提示“缺先修课，选课失败”；
本学期所选课程的上课时间有冲突，系统提示“上课时间有冲突，选课失败”；
学生一学期所选课程的学分最多不能超18学分
学生可以注销所选课程。（学生退课请求）
学院管理员可以查询学生前几学期的选课信息、可以查询课程基本信息、学生基本信息；（学院选课查询、学院课程查询、学院学生信息查询）
当学生退学时，由教务处的管理人注销学生基本信息；（教务处退学处理）
如果开课之后，学生要求退课，则由教务处的工作人员为学生注销所选课程；（教务处退课处理）
允许学生休学，教务处为休学的退学做学籍冻结处理；复学后为其办理解冻处理；（教务处休学处理、教务处复学处理）
每学期教务处为学生办理学期注册手续；没有办理学期注册的学生不能选课；（教务处注册处理）
学期末，学院工作人员负责录入学生的成绩。（学院成绩录入）
2．实验目的
1）通过本实验使学生掌握结构化需求分析的方法、过程和相应的文档内容与格式。特别是熟悉数据流程图、数据字典和IPO图三个核心技术的应用。
3．实验学时：
2学时
4．实验步骤
1）结合实验内容说明，对现有的学生选课系统进行必要的调研，了解基本的工作流程、软件功能、数据需求和界面风格。
2）分析实验内容说明和调研结果，画出系统的数据流程图。
3）编写系统的数据字典。
4）用IPO图描述系统的处理过程。
5）画出系统ER图。
5．实验结果
实验结果包括：
一份需求分析说明书，至少包括以下内容：
1）数据流程图。
2）数据字典。
3）系统的ER图。
实验2：系统设计------选课管理系统设计
1．实验内容说明：
1）对实验1的结果进行概要设计和详细设计，画出功能模块图。
2）对系统的主界面、课程基本信息录入界面、学生选课操作界面、学生选课结果浏览界面进行设计。
3）进行数据库设计。得到数据库的逻辑结构图。
2．实验学时：
2学时
3．实验结果
实验结果：一份系统设计说明书,至少包括：
1）系统功能模块图
2）用例图
3）数据库设计（包括表名、字段名、字段类型、字段大小、字段说明）。
实验3：选课管理系统编程
1．实验内容说明：
同实验1。
2．实验目的
1)通过本实验使学生通过掌握选课管理系统的实现。
3．实验学时：
4学时
4．实验步骤
根据实验1和实验2对选课管理系统的了解，选用某一高级编程语言实现本系统，并对编码通过注释进行必要的说明。
5．实验结果
实验结果包括验机和提交文档：
1)系统编译无错误，主要功能可以运行通过。（考核方式：验机）
2)系统主要功能界面。（考核方式：提交文档）
3)系统主要模块的源代码（包括注释）----（考核方式：提交文档）
实验4：选课管理系统测试
1．实验内容说明：
同实验1。
2．实验目的
1)通过本实验使学生掌握选课管理系统的测试。
2)学生在实验过程中熟练掌握测试方法。
3．实验学时：
2学时
4．实验步骤
根据实验3编写的选课管理系统，设计测试用例并实现测试。
5．实验结果
实验结果提交文档：
1)系统主要模块测试用例及测试结果
2)系统主要模块边界测试用例及测试结果

















附件1：数据流程图 参照
用Visio画出数据流程图，请参照下面的图：

附件2：功能需求表 参照
根据数据流程图和对需求的了解，给出一张功能需求表，包括需求的编号、简单描述、优先级和验证方式见下表。
编号	简述	使用者	优先级	验证方式
IPO1	查询	读者	1	分别对图书/借还书信息的有效数据、无效数据、各种组合条件进行查询，显示查询结果（结果是0条、1页、多页的情况）
IPO2	书目编辑	采编部	1	输入完整的图书信息，输入不完整的图书信息、输入错误的图书信息，重复输入
IPO3	图书注销	采购部	1	注销现有图书、注销不存在图书
IPO31	新书发布	采购部	2	缺书采购到馆后，通知登记的读者
IPO4	缺书登记	读者	2	正确的和完善的缺书信息，正确但不完善的缺书信息，重复录入缺书信息
IPO5	图书采购	采编部	2	采购缺书登记的图书，重复采购，超量采购
IPO6	预订	读者	2	正确的和完善的预订数据，正确但不完善的预订数据，无效的预订数据，相同的预订数据
IPO7	取消预订	读者	2	取消已经预订的图书、取消没有预订的图书、反复取消同一条预订记录
IPO8	更新系统参数	系统管理员	1	在XML文件中定义各种参数的值，在DTD文件中定义参数的模型，在XLS中定义参数的显示格式
IPO81	更新处罚规则	系统管理员	1	在XML文件中定义处罚规则，在DTD文件中定义参数的模型，在XLS中定义参数的显示格式
IPO9	处罚	流通部	1	输入超期处罚、丢失处罚和破损处罚信息，且测试不同日期、不同价格图书、不同页数信息
IPO10	借书处理	流通部	1	正确的和完善的借书信息，正确但不完善的借书信息，无效的借书信息，重复的借书信息，超量借书，借预订图书，续借
IPO101	有效性检查	流通部	1	输入有效/无效读者号，有效/无效图书号，借书已超量，有延期书，0库存书
IPO11	读者管理	办公室	1	输入正确读者信息、错误读者信息和无效的读者信息
IPO12	还书	流通部	1	还1本书/多本书，还过期书，还书有预订，还无效图书（没有借书记录）
IPO121	催还通知	自动触发	3	系统参数中设置催还日期，检验系统能否按照设置的日期自动发出催还邮件
IPO13	预订通知	自动触发	1	检查系统自动触发1条/多条预订到书通知，给有效邮箱、无效邮箱分别检验系统

附件4：实体关系图  参照
需求分析的一项重要任务是弄清系统将要处理的数据和数据之间的关系，也叫做数据建模。主要内容包括：要处理的主要数据对象是什么？每个数据对象的组成？这些对象当前位于何处？每个对象与其他对象的关系？
实体关系图给出了一组基本的构件：数据对象、属性、关系和各种类型指示符，主要目的是表示数据对象及其关系。为了同学们模仿，给出一个例子：

图 师-学生-课程实体关系图
实体—关系图是以迭代的方式构造的，可以采用以下的方法：
1)在需求获取的过程中，列出业务活动涉及到的“事物”，将这些“事物”演化为一组输入和输出的数据对象，以及生产信息和消费信息的外部实体。
2)一次考虑一个对象，检查这个对象和其他对象间是否存在关联，反复迭代直至定义了所有的对象—关联对。
3)定义每个实体的属性。
4)规范化并复审实体—关系图。
附件5：数据字典 参照
对选课系统的所有数据库表进行说明，格式请参照下面的表格：
编号：DS2                                    名称：图书信息记录
名称	简称	键值	类型	长度	值域	初值	备注
图书编号	BookID	P	字符	100			
书名	BookNM		字符	100			
类型	Subject		字符	100			可选择
作者	Author		字符	100			
图书ISBN	ISBN		字符	100			
出版社	Press		字符	20			
出版日期	Press_data		日期	8			
总的册数	Total		数字				
关键字	Keywords		字符	100			
当前在库数量	Count		数字				
注意：这些表格中有些项在分析阶段可能是空白，将在设计过程中逐步完善，现在不必追究它们的具体内容。
3、数据库表的SQL代码。学生用PowerDesign建立E-R图，创建数据库表并生成SQL语句。例如：创建一个学生表，有学生号(主键)，姓名、学生年龄。
create table Student				--学生表
(
	StudentID int not null primary key,		--学号
	StudentName varchar(10) , 		--学生姓名
	Age	int not null,			--学生年龄
	
)

附件6：系统界面 参照
1、给出系统的主界面
系统的界面风格应保持一致，配色协调统一的原则采用浅蓝色的主色调，文字的大小9号和宋体字。
请参考下面的系统界面：

注意：不必一定设计成标签页
2、设计课程基本信息录入界面草图
课程基本信息的录入界面要求操作方面，美观，请参考下面的界面设计。

 3、设计学生选课操作的界面。
请参考北方工业大学选课网站的选课界面进行设计，但是颜色要与整个系统的界面保持一致。
4、学生选课的浏览界面。浏览界面可参考下面的图设计：

附件7：界面设计规范  参考
菜单
软件左边菜单的设计风格：
1.菜单的深度为一层。
2.字体大小为12px。
3.颜色为黑色。
4.字体高度为25px。
按钮
软件所有按钮的设计风格：
1.字体大小为12px。
2.字体颜色为黑色。
3.按钮高度12px，宽度为65px。
4.字体对齐方式为居中对齐。
输入框
软件中输入框的设计风格：
1.输入字体大小为12px。
2.输入字体居左对齐。
3.输入字体的颜色为黑色。
4.输入框边框宽度为1px。
标题文字
在操作区中查询列表中的标题文字风格：
1.字体大小为12px.。
2.文字颜色为白色。
3.文字高度为20px。
4.对齐方式为居中对齐。
表格
软件中表格所采用的设计风格：
1.输入字体的大小为12px。
2.表格行高为20px。
3.字体颜色为黑色。
4.文字对齐方式为居中。
登录框
软件登录框的所采用的设计风格：
1.输入字体的大小为12px。
2.字体颜色为灰色。
3.输入框的背景颜色为白色。
4.输入框的边界宽度为1px。
检索输入框
软件中检索输入框的设计所采用的设计风格：
1.输入字体的大小为12px。
2.输入框的高度为26px。
3.输入框背景色为白色。
4.输入文字的对齐方式为左对齐。

【实验报告要求】
《数据库专题训练》报告内容包括两个方面：封面和报告内容。
封面占单独一页，固定格式。样本见下面附件1（封面样本）。
报告内容包括：实验题目、实验目的和要求，实验步骤、实验结果及实验总结
报告字体要求：正文为“宋体小四”，行间距为单行倍距，纸张统一为A4纸。
