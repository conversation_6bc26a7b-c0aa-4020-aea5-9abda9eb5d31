"use client"

import { Course } from "@/lib/resp";
import { useFetch } from "@/lib/use-fetch-api";
import { <PERSON><PERSON>, <PERSON>ton, Card, CardBody, CardFooter, CardHeader, Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@heroui/react";
import { useEffect, useState } from "react";

export function ListCourseCard() {

    const { loadState, error, data, doRequest } = useFetch({
        url: "/api/course",
        method: "GET",
        loadFirst: true,
    })

    const [showError, setShowError] = useState(false)
    const [courses, setCourses] = useState<Course[]>([])

    // 失败
    useEffect(() => {
        if (loadState === "error") {
            setShowError(true)
        }
    }, [loadState])

    // 成功
    useEffect(() => {
        if (loadState !== "ok") return;
        if (data === null || data === undefined) return;

        setCourses(data as Course[])

    }, [loadState, data])

    return <Card>
        <CardHeader>
            <h2>所有课程</h2>
        </CardHeader>
        <CardBody className="flex flex-col gap-2">

            {
                loadState === "loading" ? <div>加载中...</div> : <></>
            }

            <Alert color={"danger"} isVisible={showError} onClose={() => setShowError(false)}>
                {`${error?.msg} (${error?.code})`}
            </Alert>

            <Table>
                <TableHeader>
                    <TableColumn>编号</TableColumn>
                    <TableColumn>名称</TableColumn>
                    <TableColumn>教师</TableColumn>
                    <TableColumn>教室</TableColumn>

                    <TableColumn>学分</TableColumn>
                    <TableColumn>上课时间</TableColumn>
                    <TableColumn>先修课程</TableColumn>
                    <TableColumn>说明</TableColumn>
                </TableHeader>

                <TableBody>
                    {
                        courses.map((course, index) => {
                            return <TableRow key={index}>
                                <TableCell>{course.ID}</TableCell>
                                <TableCell>{course.NAME}</TableCell>
                                <TableCell>{course.TEACHER}</TableCell>
                                <TableCell>{course.CLASSROOM}</TableCell>

                                <TableCell>{course.CREDIT}</TableCell>
                                <TableCell>{course.TIME}</TableCell>
                                <TableCell>{course.PRE_COURSE}</TableCell>
                                <TableCell>{course.NOTE}</TableCell>

                            </TableRow>
                        })
                    }
                </TableBody>
            </Table>
        </CardBody>

        <CardFooter>
            <Button color="primary" onPress={doRequest} isLoading={loadState === "loading"}>刷新</Button>
        </CardFooter>
    </Card>
}