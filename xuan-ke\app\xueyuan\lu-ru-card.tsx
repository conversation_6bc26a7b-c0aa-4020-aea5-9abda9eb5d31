"use client";

import { useFetch } from "@/lib/use-fetch-api";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, CardBody, CardHeader, Input } from "@heroui/react";
import { useEffect, useRef, useState } from "react";

export function LuRuCard() {

    const body = useRef<any>(null)

    const { data, error, loadState, doRequest } = useFetch({
        url: "/api/student/add",
        method: "POST",
        loadFirst: false,
        body: body.current,
    })

    const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault()

        const data = new FormData(e.currentTarget)

        body.current = Object.fromEntries(data.entries())
        doRequest()
    }

    const [showError, setShowError] = useState(false)
    const [showSuccess, setShowSuccess] = useState(false)

    // 成功
    useEffect(() => {
        if (loadState === "ok") {
            setShowSuccess(true)
        }
    }, [loadState, data])

    // 失败
    useEffect(() => {
        if (loadState === "error") {
            setShowError(true)
        }
    }, [loadState])

    return <Card>
        <CardHeader><h2>学生信息录入</h2></CardHeader>
        <CardBody>
            <form className="flex flex-col gap-4" onSubmit={onSubmit}
                onFocus={() => { setShowError(false); setShowSuccess(false) }}>
                <Input
                    name="id" label={"学号"}
                ></Input>

                <Input
                    name="name" label={"姓名"}></Input>

                <Input
                    name="note" label={"其它信息"}></Input>

                <Button isLoading={loadState === "loading"} color="primary" type={"submit"}>录入</Button>

                <Alert color={"danger"} isVisible={showError} onClose={() => setShowError(false)}>
                    {`${error?.msg} (${error?.code})`}
                </Alert>

                <Alert color={"success"} isVisible={showSuccess} onClose={() => setShowSuccess(false)}>
                    录入成功！
                </Alert>
            </form>
        </CardBody>

    </Card>
}