"use client"

import { useFetch } from "@/lib/use-fetch-api"
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, CardBody, CardHeader, Input } from "@heroui/react"
import { useEffect, useState } from "react"

export function XuanKeCard() {

    const [courseId, setCourseId] = useState("")

    const [showOk, setShowOk] = useState(false)
    const [showError, setShowError] = useState(false)

    const { doRequest, loadState, data, error } = useFetch({
        url: "/api/xuan-ke/add",
        method: "POST",
        body: {
            course_id: courseId,
        },
        loadFirst: false,
    })

    const onSubmit = (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault()
        const data = new FormData(event.currentTarget)
        setCourseId(data.get("course_id") as string)
        doRequest()
    }

    // 成功
    useEffect(() => {
        if (loadState === "ok") {
            setShowOk(true)
        }
        if (loadState === "error") {
            setShowError(true)
        }
    }, [loadState])

    return <Card>
        <CardHeader><h2>选课</h2></CardHeader>
        <CardBody>
            <form className="flex flex-col gap-4"
                onSubmit={onSubmit}
                onFocus={() => {
                    setShowOk(false)
                    setShowError(false)
                }}
            >
                <Input label={"课程号"} placeholder="请输入课程号"
                    name={"course_id"}
                >
                </Input>

                <Alert color={"danger"} isVisible={showError}>
                    {`${error?.msg} (${error?.code})`}
                </Alert>

                <Alert color={"success"} isVisible={showOk}>
                    选课成功
                </Alert>

                <Button isLoading={loadState === "loading"} type={"submit"} color={"primary"}>确认</Button>
            </form>
        </CardBody>
    </Card>

}