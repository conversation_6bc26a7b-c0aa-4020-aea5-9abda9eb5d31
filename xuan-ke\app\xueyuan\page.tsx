import { getSession } from "@/lib/cookie";
import { XueYuanPage } from "./xueyuan";
import { redirect } from "next/navigation";
import Link from "next/link";

export default async function Page() {

    const session = await getSession()

    if (session === null) {
        redirect("/login")
    }

    if (session.user.ROLE !== "学院") {
        return <div className="flex flex-col gap-4">
            <h2 className="text-red-500 font-bold text-3xl">仅学院管理员可访问</h2>
            <p>当前用户：{session.user.ID}、{session.user.ROLE}</p>
            <Link className="underline text-blue-500" href={"/"}>回到首页</Link>
        </div>
    }

    return <XueYuanPage id={session.user.ID} role={session.user.ROLE} />
}