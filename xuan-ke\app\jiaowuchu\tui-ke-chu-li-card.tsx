"use client"

import { useFetch } from "@/lib/use-fetch-api";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, CardBody, CardHeader, dateRangePicker, Input } from "@heroui/react";
import { useEffect, useRef, useState } from "react";

export function TuiKeChuLiCard() {

    const bodyRef = useRef<any>(null)

    const { doRequest, error, loadState } = useFetch({
        url: "/api/xuan-ke/tui-ke/approve",
        method: "POST",
        body: bodyRef.current,
        loadFirst: false,
    })

    const onSubmit = (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault()
        const data = new FormData(event.currentTarget)
        bodyRef.current = Object.fromEntries(data.entries())
        doRequest()
    }

    const [showOk, setShowOk] = useState(false)
    const [showError, setShowError] = useState(false)

    useEffect(() => {
        if (loadState === "ok") {
            setShowOk(true)
        }

        if (loadState === "error") {
            setShowError(true)
        }

    }, [loadState])

    return <Card>
        <CardHeader><h2>退课处理</h2></CardHeader>
        <CardBody>

            <form className="flex flex-col gap-2" onSubmit={onSubmit} onFocus={() => {
                setShowOk(false)
                setShowError(false)
            }}>
                <Input name="student_id" label="学号"></Input>

                <Input name="course_id" label={"课程号"}></Input>

                <Alert color={"success"} isVisible={showOk}>
                    操作成功，已同意退课
                </Alert>

                <Alert color={"danger"} isVisible={showError}>
                    {`${error?.msg} (${error?.code})`}
                </Alert>

                <Button type={"submit"} color={"primary"} isLoading={loadState === "loading"}>确认</Button>
            </form>

        </CardBody>
    </Card>
}