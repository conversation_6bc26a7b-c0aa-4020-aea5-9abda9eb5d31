import { getSession } from "@/lib/cookie";
import db from "@/lib/db";
import { MyResp } from "@/lib/resp";
import { sessionManager } from "@/lib/session";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
    const { password, password_repeat } = await req.json();

    //
    const session = await getSession();

    if (session === null) {
        const r: MyResp = {
            code: "no_login",
            msg: "未登录",
            data: null,
        }
        return NextResponse.json(r)
    }

    if (password === null || password === undefined || password === "") {
        const r: MyResp = {
            code: "password_empty",
            msg: "密码不能为空",
            data: null,
        }
        return NextResponse.json(r)
    }

    if (password.length < 6) {
        const r: MyResp = {
            code: "password_too_short",
            msg: "密码长度不能小于6",
            data: null,
        }
        return NextResponse.json(r)
    }

    if (password !== password_repeat) {
        const r: MyResp = {
            code: "password_not_equal",
            msg: "两次密码不一致",
            data: null,
        }
        return NextResponse.json(r)
    }

    try {
        const [res] = await db.execute(`
        UPDATE user
        SET PASSWORD = ?
        WHERE ID = ?;`, [password, session.user.ID])
    } catch (e) {
        const r: MyResp = {
            code: "db_error",
            msg: "数据库错误: " + e,
            data: null,
        }
        return NextResponse.json(r)
    }

    // 退出登录
    // 删除Cookie
    const cookieStore = await cookies();
    cookieStore.delete("sid")

    // 删除会话
    sessionManager.removeSession(session.id)

    const r: MyResp = {
        code: "ok",
        msg: "修改密码成功",
        data: null,
    }
    return NextResponse.json(r)
}