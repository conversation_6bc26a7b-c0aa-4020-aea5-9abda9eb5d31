import { getSession } from "@/lib/cookie";
import { StudentPage } from "./student";
import { redirect } from "next/navigation";

export default async function Page() {

    const session = await getSession();

    if (session === null) {
        redirect("/login")
    }

    if (session.user.ROLE !== "学生") {
        return <h1>您不是学生</h1>
    }

    const name = session.student?.NAME;


    return <StudentPage name={name} />
}