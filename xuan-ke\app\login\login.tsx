"use client";

import { User } from "@/lib/resp";
import { useFetch } from "@/lib/use-fetch-api";
import { Button } from "@heroui/button";
import { Input } from "@heroui/input";
import { Alert } from "@heroui/react";
import { useCallback, useEffect, useRef, useState } from "react";

export function Login() {

    const bodyRef = useRef<any>(null)

    const [showError, setShowError] = useState(false)

    const { loadState, error, data, doRequest } = useFetch({
        url: "/api/login",
        method: "POST",
        loadFirst: false,
        body: bodyRef.current,
    })

    // 失败
    useEffect(() => {
        if (loadState !== "error") return;
        if (error === null || error === undefined) return;
        setShowError(true)
    }, [loadState, error])

    // 成功
    useEffect(() => {
        if (loadState !== "ok") return;

        if (data === null || data === undefined) return;

        const res = data as {
            user: User
        }

        // todo
        // window.alert(res.sid)

        // 根据用户角色重定向
        if (res.user.ROLE === "学生") {
            window.location.href = "/student"

        } else if (res.user.ROLE === "教务处") {
            window.location.href = "/jiaowuchu"
        } else if (res.user.ROLE === "学院") {
            window.location.href = "/xueyuan"
        } else {
            window.alert("未知角色: " + res.user.ROLE)
        }
    }, [loadState, data])

    const onSubmit = useCallback((e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        const data = new FormData(e.currentTarget);

        bodyRef.current = Object.fromEntries(data.entries());

        doRequest()
    }, [])

    return <>
        <div className="w-fit mx-auto shadow-lg rounded-lg p-8">
            <h1 className="text-2xl font-bold mb-4 text-center">欢迎登录选课系统</h1>

            <form className="flex flex-col gap-4 mb-4" onSubmit={onSubmit} onFocus={() => setShowError(false)}>
                <Input label={"用户名"}
                    name={"username"}
                    isRequired
                    placeholder={"请输入用户名/姓名/学号"}
                ></Input>

                <Input label={"密码"}
                    name={"password"}
                    type={"password"}
                    isRequired
                    placeholder={"请输入密码"}
                ></Input>

                <Button type={"submit"} color={"primary"} isLoading={loadState === "loading"}>登录</Button>
            </form>

            <Alert color={"danger"} isVisible={showError} onClose={() => setShowError(false)}>
                {error && <div className="text-red-500">{error.msg}</div>}
            </Alert>
        </div>
    </>
}