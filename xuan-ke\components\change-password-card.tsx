import { useFetch } from "@/lib/use-fetch-api";
import { <PERSON><PERSON>, Card, CardBody, CardHeader, Input } from "@heroui/react";
import { useEffect, useRef } from "react";


export function ChangePasswordCard() {

    const bodyRef = useRef<any>(null);

    const { doRequest, loadState, error } = useFetch({
        url: "/api/password",
        method: "POST",
        body: bodyRef.current,
        loadFirst: false,
    });

    const onSubmit = (e: any) => {
        e.preventDefault();
        const data = new FormData(e.target);
        const data2 = Object.fromEntries(data.entries());
        bodyRef.current = data2;
        doRequest();
    }

    useEffect(() => {
        if (loadState === "ok") {
            window.alert("密码修改成功，请重新登录");
            window.location.reload();
        }

        if (loadState === "error") {
            window.alert(`密码修改失败: ${error?.msg} (${error?.code})`);
        }

    }, [loadState, error])

    return <Card>
        <CardHeader><h2>修改密码</h2></CardHeader>
        <CardBody>
            <form className="flex flex-col gap-4" onSubmit={onSubmit}>
                <Input
                    name="password"
                    type={"password"}
                    label="新密码"
                    autoComplete="new-password"
                ></Input>

                <Input
                    name="password_repeat"
                    type={"password"}
                    label="确认密码"
                    autoComplete="new-password"
                >
                </Input>

                <Button type="submit" color={"primary"}>确认</Button>

            </form>
        </CardBody>
    </Card>
}