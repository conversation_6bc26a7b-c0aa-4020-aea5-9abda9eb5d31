"use client";

import { useFetch } from "@/lib/use-fetch-api";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, CardBody, CardHeader, Input } from "@heroui/react";
import { useEffect, useRef, useState } from "react";

export function AddCourseCard() {

    const body = useRef<any>(null)

    const { doRequest, error, loadState } = useFetch({
        url: "/api/course/add",
        method: "POST",
        body: body.current,
        loadFirst: false,
    })

    const onSubmit = (e: any) => {
        e.preventDefault()
        const data = new FormData(e.target)
        body.current = Object.fromEntries(data.entries())
        doRequest()
    }

    const [showError, setShowError] = useState(false);
    const [showSuccessMsg, setShowSucessMsg] = useState("");

    // 错误
    useEffect(() => {
        if (loadState === "error") {
            setShowError(true)
        }
    }, [loadState])

    // 成功
    useEffect(() => {
        if (loadState === "ok") {
            setShowSucessMsg("课程录入成功")
        }
    }, [loadState])


    return <Card>
        <CardHeader><h2>课程录入</h2></CardHeader>

        <CardBody>
            <form className="flex flex-col gap-4" onSubmit={onSubmit}
                onFocus={() => {
                    setShowError(false)
                    setShowSucessMsg("")
                }}
            >

                <div className="flex flex-row flex-wrap gap-4 w-full justify-stretch items-stretch">
                    <div className="flex flex-col gap-4 flex-1">
                        <Input name={"id"} label={"课程编号"}></Input>
                        <Input name={"name"} label={"课程名称"}></Input>
                        <Input name={"teacher"} label={"授课教师"}></Input>
                        <Input name={"credit"} label={"学分"}></Input>
                    </div>

                    <div className="flex flex-col gap-4 flex-1">
                        <Input name={"time"} label={"上课时间"}></Input>
                        <Input name={"location"} label={"上课地点"}></Input>
                        <Input name={"description"} label={"课程说明"}></Input>
                        <Input name={"pre_course"} label={"先修课程"}></Input>
                    </div>
                </div>






                <Alert color={"danger"} isVisible={showError} onClose={() => setShowError(false)}>
                    {`${error?.msg} (${error?.code})`}
                </Alert>

                <Alert color={"success"} isVisible={showSuccessMsg !== ""} onClose={() => setShowSucessMsg("")}>
                    {showSuccessMsg}
                </Alert>

                <Button color={"primary"} type={"submit"}>提交</Button>
            </form>
        </CardBody>
    </Card>
}