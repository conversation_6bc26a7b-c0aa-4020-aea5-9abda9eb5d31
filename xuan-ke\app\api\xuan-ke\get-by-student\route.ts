import { getSession } from "@/lib/cookie";
import db from "@/lib/db";
import { MyResp, <PERSON><PERSON><PERSON><PERSON> } from "@/lib/resp";
import { NextRequest, NextResponse } from "next/server";

export async function GET(_req: NextRequest) {

    const session = await getSession();

    if (session === null) {
        const r: MyResp = {
            code: "no_login",
            msg: "学生登录后再查询选课",
            data: null
        }
        return NextResponse.json(r);
    }

    if (session.student === null || session.student === undefined) {
        const r: MyResp = {
            code: "not_student",
            msg: "不是学生",
            data: null
        }
        return NextResponse.json(r);
    }

    try {
        const [res] = await db.execute(`
        SELECT ID, SID, CID, TIME, SCORE, DEL_APP
        FROM xuan_ke
        WHERE SID = ?
        ORDER BY ID DESC;`, [session.student.ID]);

        const list = res as <PERSON><PERSON><PERSON><PERSON>[];

        const r: MyResp = {
            code: "ok",
            msg: "查询成功",
            data: list
        }

        return NextResponse.json(r);

    } catch (e) {
        const r: MyResp = {
            code: "db_error",
            msg: "数据库错误: " + e,
            data: null
        }
        return NextResponse.json(r);
    }
}