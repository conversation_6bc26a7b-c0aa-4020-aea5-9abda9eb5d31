import { getSession } from "@/lib/cookie";
import db from "@/lib/db";
import { MyResp, Student } from "@/lib/resp";
import { NextResponse } from "next/server";

// 获取所有学生信息
export async function GET() {

    // 检查权限
    const sess = await getSession();

    if (sess === null) {
        const resp: MyResp = {
            code: "no_login",
            msg: "请先登录",
            data: null,
        }
        return NextResponse.json(resp)
    }

    // 检查权限
    if (sess.user.ROLE !== "教务处" && sess.user.ROLE !== "学院") {
        const resp: MyResp = {
            code: "no_permission",
            msg: "仅学院和教务处",
            data: null,
        }
        return NextResponse.json(resp)
    }

    const resp = await db.query(`
        SELECT ID, NAME, NOTE, SUSPENDED, REGISTERED
        FROM xue_sheng;`)

    const students = resp[0] as Student[]

    const resp2: MyResp<Student[]> = {
        code: "ok",
        msg: "ok",
        data: students
    }

    return NextResponse.json(resp2)
}