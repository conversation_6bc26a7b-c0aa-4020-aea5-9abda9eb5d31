"use client"

import { useFetch } from "@/lib/use-fetch-api"
import { But<PERSON> } from "@heroui/button"
import { useEffect } from "react";

export function LogoutButton() {
    const { loadState, error, data, doRequest } = useFetch({
        url: "/api/logout",
        method: "POST",
        loadFirst: false
    });

    // 成功
    useEffect(() => {
        if (loadState === "ok") {
            window.location.reload()
        }
    }, [loadState])

    // 失败
    useEffect(() => {
        if (loadState === "error") {
            window.alert(error?.code + " " + error?.msg)
        }
    }, [loadState, error])


    return <Button color={"primary"}
        isLoading={loadState === "loading"} onPress={doRequest}>退出登录</Button>
}