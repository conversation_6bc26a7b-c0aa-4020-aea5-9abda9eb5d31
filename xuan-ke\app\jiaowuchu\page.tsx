import { getSession } from "@/lib/cookie";
import JiaowuchuPage from "./jiaowuchu";
import { redirect } from "next/navigation";
import { Button } from "@heroui/react";
import Link from "next/link";

export default async function Page() {

    const session = await getSession()

    if (session === null) {
        redirect("/login")
    }

    // 检查权限是否为教务处
    if (session.user.ROLE !== "教务处") {
        return <div className="flex flex-col gap-4">
            <h2 className="text-red-500 font-bold text-3xl">仅教务处管理员可访问</h2>
            <p>当前用户：{session.user.ID}、{session.user.ROLE}</p>
            <Link className="underline text-blue-500" href={"/"}>回到首页</Link>
        </div>
    }


    return <JiaowuchuPage id={session.user.ID} role={session.user.ROLE}/>
}