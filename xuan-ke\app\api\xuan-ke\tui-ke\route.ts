import { getSession } from "@/lib/cookie";
import db from "@/lib/db";
import { MyResp } from "@/lib/resp";
import { ResultSetHeader } from "mysql2";
import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {

    // 获取search参数
    const searchParams = req.nextUrl.searchParams;
    const id = searchParams.get("id");
    if (id === null) {
        const r: MyResp = {
            code: "no_id",
            msg: "没有id",
            data: null
        }
        return NextResponse.json(r)
    }

    const session = await getSession();

    if (session === null) {
        const r: MyResp = {
            code: "no_login",
            msg: "未登录",
            data: null
        }
        return NextResponse.json(r)
    }

    const stu = session.student;

    if (stu === null || stu === undefined) {
        const r: MyResp = {
            code: "not_student",
            msg: "不是学生",
            data: null
        }
        return NextResponse.json(r)
    }

    try {
        const [res] = await db.execute(`UPDATE xuan_ke
        SET DEL_APP='已提交'
        WHERE ID = ?
        AND SID = ?;`, [id, stu.ID])

        const r = res as ResultSetHeader

        if (r.affectedRows === 0) {
            const r: MyResp = {
                code: "no_xuan_ke",
                msg: "没有选课记录",
                data: null
            }
            return NextResponse.json(r)
        }
    } catch (e) {
        const r: MyResp = {
            code: "db_err",
            msg: "数据库错误: " + e,
            data: null
        }
        return NextResponse.json(r)
    }

    const r: MyResp = {
        code: "ok",
        msg: "退课成功",
        data: null
    }
    return NextResponse.json(r)
}