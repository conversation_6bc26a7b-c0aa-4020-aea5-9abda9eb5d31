import { useFetch } from "@/lib/use-fetch-api";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, CardBody, CardHeader, Input } from "@heroui/react";
import { useEffect, useState } from "react";


export function ZhuCeCard() {

    const [studentId, setStudentId] = useState("")

    const { doRequest, error, data, loadState } = useFetch({
        url: "/api/student/zhu-ce",
        method: "POST",
        body: {
            "student_id": studentId
        },
        loadFirst: false
    })

    const onSubmit = (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault()
        const data = new FormData(event.currentTarget)
        setStudentId(data.get("student_id") as string)
        doRequest()
    }

    const [showError, setShowError] = useState(false)
    const [showSuccess, setShowSuccess] = useState(false)

    useEffect(() => {
        if (loadState === "error") {
            setShowError(true)
        }
        if (loadState === "ok") {
            setShowSuccess(true)
        }
    }, [loadState])

    return <Card>
        <CardHeader><h2>学生注册</h2></CardHeader>
        <CardBody>
            <form className="flex flex-col gap-4" onSubmit={onSubmit} onFocus={() => {
                setShowError(false)
                setShowSuccess(false)
            }}>
                <Input label="学生学号" name={"student_id"}></Input>

                <Alert color={"danger"} isVisible={showError}>
                    {`${error?.msg} (${error?.code})`}
                </Alert>

                <Alert color={"success"} isVisible={showSuccess}>
                    {
                        data ? data.message : ""
                    }
                </Alert>

                <Button color={"primary"} type={"submit"} isLoading={loadState === "loading"}>确认</Button>
            </form>

        </CardBody>
    </Card>
}