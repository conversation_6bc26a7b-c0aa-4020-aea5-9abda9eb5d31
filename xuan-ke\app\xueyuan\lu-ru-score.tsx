import { useFetch } from "@/lib/use-fetch-api";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, CardBody, CardHeader, Input } from "@heroui/react";
import { useEffect, useRef, useState } from "react";

export function LuRuScore() {

    const bodyRef = useRef<any>()

    const { doRequest, loadState, error } = useFetch({
        url: "/api/xuan-ke/score",
        method: "POST",
        body: bodyRef.current,
        loadFirst: false
    });

    const [showOk, setShowOk] = useState(false)
    const [showError, setShowError] = useState(false)

    useEffect(() => {
        if (loadState === "ok") {
            setShowOk(true)
        }

        if (loadState === "error") {
            setShowError(true)
        }

    }, [loadState])

    const onSubmit = (e: any) => {
        e.preventDefault()
        const data = new FormData(e.target)
        bodyRef.current = Object.fromEntries(data.entries())
        doRequest()
    }

    return <Card>
        <CardHeader><h2>录入成绩</h2></CardHeader>

        <CardBody>
            <form className="flex flex-col gap-4" onSubmit={onSubmit}
                onFocus={() => { setShowOk(false); setShowError(false) }}>
                <Input name="student_id" label={"学号"} aria-label="学号"></Input>
                <Input name={"course_id"} label={"课程号"} aria-label="课程号"></Input>

                <Input name={"score"} label={"分数"}>分数</Input>

                <Alert color={"danger"} isVisible={showError}>
                    {`${error?.msg} (${error?.code})`}
                </Alert>

                <Alert color={"success"} isVisible={showOk}>
                    成绩录入成功
                </Alert>

                <Button color={"primary"} type={"submit"} isLoading={loadState === "loading"}>录入</Button>
            </form>
        </CardBody>

    </Card>
}