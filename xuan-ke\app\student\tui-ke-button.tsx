import { useFetch } from "@/lib/use-fetch-api";
import { But<PERSON> } from "@heroui/button";
import { useEffect, useRef } from "react";

export function TuiKeButton(props: {
    id: number
    onOk?: () => void
}) {

    const { doRequest, loadState, error, data } = useFetch({
        url: "/api/xuan-ke/tui-ke?id=" + props.id,
        method: "POST",
        loadFirst: false,
    })

    const onOkRef = useRef(props.onOk)
    onOkRef.current = props.onOk

    useEffect(() => {
        if (loadState === "ok") {
            onOkRef.current?.()
            window.alert("已申请退课")
        }
        if (loadState === "error") {
            window.alert(`申请退课失败: ${error?.msg} (${error?.code})`)
        }

    }, [loadState, error])


    return <Button isLoading={loadState === "loading"} onPress={doRequest}>申请退课</Button>
}