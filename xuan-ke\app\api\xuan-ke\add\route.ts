import { getSession } from "@/lib/cookie";
import db from "@/lib/db";
import { MyResp } from "@/lib/resp";
import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {

    const session = await getSession();

    if (session === null) {
        const r: MyResp = {
            code: "no_login",
            msg: "请先登录",
            data: null
        }

        return NextResponse.json(r);
    }

    const stu = session.student;

    if (stu === null || stu === undefined) {
        const r: MyResp = {
            code: "not_student",
            msg: "不是学生",
            data: null
        }
        return NextResponse.json(r);
    }

    const { course_id } = await req.json();

    if (course_id === null || course_id === undefined || course_id === "") {
        const r: MyResp = {
            code: "no_course_id",
            msg: "课程ID为空",
            data: null
        }
        return NextResponse.json(r);
    }

    // 检查是否已经选课
    try {
        const [res] = await db.execute(`
            SELECT ID
            FROM xuan_ke
            WHERE SID = ?
            AND CID = ?;`, [stu.ID, course_id]);


        const list = res as any[];

        if (list.length > 0) {
            const r: MyResp = {
                code: "already_xuan_ke",
                msg: "已经选课，选别的课程试试吧",
                data: null
            }
            return NextResponse.json(r);
        }
    } catch (e) {
        const r: MyResp = {
            code: "db_error",
            msg: "数据库错误: " + e,
            data: null
        }
        return NextResponse.json(r);
    }

    // 选课
    try {
        await db.execute(`
            INSERT INTO xuan_ke (SID, CID, TIME, SCORE, DEL_APP)
            VALUES (?, ?, UNIX_TIMESTAMP(), NULL, NULL);`, [stu.ID, course_id]);
    } catch (e) {
        const r: MyResp = {
            code: "db_error",
            msg: "数据库错误: " + e,
            data: null
        }
        return NextResponse.json(r);
    }

    const r: MyResp = {
        code: "ok",
        msg: "选课成功",
        data: null
    }
    return NextResponse.json(r);
}