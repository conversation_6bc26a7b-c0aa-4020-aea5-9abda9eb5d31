import { cookies } from "next/headers";
import { session<PERSON>anager, Session } from "./session";
import { Student, User } from "./resp";

export async function getSession() {
    const cookieStore = await cookies()

    const sid = cookieStore.get("sid")

    // console.log("sid", sid)

    if (sid === undefined || sid === null) return null;

    const sess = sessionManager.getSession(sid.value)

    // console.log("sess", sess)

    return sess
}

export async function createSession(user: User, student: Student | null) {
    // 创建会话
    const sess = sessionManager.addSession(Session.create(user, student))

    // cookie
    const cookieStore = (await cookies())
    cookieStore.set("sid", sess.id, {
        httpOnly: true, // 仅服务器端可读
        sameSite: "strict",
        path: "/",
        maxAge: 60 * 60 * 24 * 30 // 30天
    })

    return sess
}