
export interface MyResp<T = unknown> {
    code: string;
    msg: string;
    data: T;
}

export interface Course {
    ID: number;
    NAME: string;
    CLASSROOM: string;
    TEACHER: string;
    TIME: string;
    PRE_COURSE: number;
    CREDIT: number;
    NOTE: string
}

export interface User {
    ID: string,
    PASSWORD: string,
    ROLE: string,
}

/*
CREATE TABLE IF NOT EXISTS xue_sheng
(
    ID         int unsigned auto_increment primary key not null,
    NAME       char(16)                                not null,
    NOTE       varchar(64)                             not null,
    SUSPENDED  bool                                    not null,
    REGISTERED bool                                    not null
);
*/

export interface Student {
    ID: number;
    NAME: string;
    NOTE: string;
    SUSPENDED: boolean;
    REGISTERED: boolean;
}

export interface XuanKe {
    ID: number;
    SID: number;
    CID: number;
    TIME: number;
    DEL_APP: string | null;
    SCORE: number | null;
}