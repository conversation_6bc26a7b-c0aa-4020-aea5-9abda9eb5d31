import { CourseLink } from "@/components/course-link";
import db from "@/lib/db";
import { Course } from "@/lib/resp";

export default async function Page(props: {
    searchParams: any
}) {

    let { searchParams } = props;
    searchParams = await searchParams;

    // 查询课程信息
    const { id } = searchParams;

    const [res] = await db.execute(`
        SELECT ID,
       NAME,
       CLASSROOM,
       TEACHER,
       TIME,
       PRE_COURSE,
       CREDIT,
       NOTE
    FROM ke_cheng
    WHERE ID = ?;`, [id])

    const list = res as Course[]

    if (list.length === 0) {
        return <h1 className="text-red-500 font-bold text-3xl">课程不存在</h1>
    }

    const c = list[0]

    return <div className="flex flex-col space-y-4 border-purple-400 border-1 shadow-lg rounded-lg p-8">
        <h1 className="text-3xl font-bold">{c.NAME}</h1>
        <p>课程ID：{c.ID}</p>
        <p>教室：{c.CLASSROOM}</p>
        <p>教师：{c.TEACHER}</p>
        <p>时间：{c.TIME}</p>

        {
            c.PRE_COURSE as any === "" || c.PRE_COURSE === null || c.PRE_COURSE === 0 ?
                <p>先修课程：无</p> :
                <p>先修课程：<CourseLink id={c.PRE_COURSE}>{String(c.PRE_COURSE) + " 点击查看"}</CourseLink></p>
        }

        <p>学分：{c.CREDIT}</p>
        <p>说明：{c.NOTE}</p>
    </div>
}