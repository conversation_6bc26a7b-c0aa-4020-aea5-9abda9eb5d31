import { useFetch } from "@/lib/use-fetch-api";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, CardBody, CardHeader, Input, Select, SelectItem } from "@heroui/react";
import { useEffect, useRef, useState } from "react";

export function XiuFuXueCard() {

    const bodyRef = useRef<any>()
    const { doRequest, loadState, error } = useFetch({
        url: "/api/student/xiu-fu-xue",
        method: "POST",
        body: bodyRef.current,
        loadFirst: false
    })

    const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault()
        const data = new FormData(e.currentTarget)
        bodyRef.current = Object.fromEntries(data.entries())
        doRequest()
    }

    const [showOk, setShowOk] = useState(false)
    const [showError, setShowError] = useState(false)

    useEffect(() => {
        if (loadState === "ok") {
            setShowOk(true)
        }
        if (loadState === "error") {
            setShowError(true)
        }

    }, [loadState])

    return <Card>
        <CardHeader><h2>修复学处理</h2></CardHeader>
        <CardBody>
            <form className="flex flex-col gap-4" onSubmit={onSubmit}
                onFocus={() => {
                    setShowOk(false)
                    setShowError(false)
                }}
            >
                <Input label={"学号"} name="student_id"></Input>

                <Select label={"操作"} defaultSelectedKeys={["xiu-xue"]} name={"operation"}>
                    <SelectItem key={"xiu-xue"}>休学</SelectItem>
                    <SelectItem key={"fu-xue"}>复学</SelectItem>
                </Select>

                <Alert color={"danger"} isVisible={showError}>
                    {`${error?.msg} (${error?.code})`}
                </Alert>

                <Alert color={"success"} isVisible={showOk}>
                    操作成功
                </Alert>

                <Button type={"submit"} color={"primary"}>确认</Button>
            </form>
        </CardBody>
    </Card>
}