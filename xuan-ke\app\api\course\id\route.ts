import db from "@/lib/db";
import { Course, MyResp } from "@/lib/resp";
import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {
    const id = req.nextUrl.searchParams.get("id")
    if (id === null) {
        const r: MyResp = {
            code: "no_id",
            msg: "no id",
            data: null

        }
        return NextResponse.json(r)
    }

    try {
        const [res] = await db.execute(`
        SELECT ID,
       NAME,
       CLASSROOM,
       TEACHER,
       TIME,
       PRE_COURSE,
       CREDIT,
       NOTE
    FROM ke_cheng
    WHERE ID = ?;`, [id])

        const list = res as Course[]

        if (list.length === 0) {
            const r: MyResp = {
                code: "no_course",
                msg: "没有找到课程",
                data: null
            }
            return NextResponse.json(r)
        }

        const r: MyResp = {
            code: "ok",
            msg: "",
            data: list[0]
        }
        return NextResponse.json(r)
    } catch (e) {
        const r: MyResp = {
            code: "db_error",
            msg: "数据库错误： " + e,
            data: null

        }
        return NextResponse.json(r)
    }
}