"use client"
import { <PERSON><PERSON><PERSON><PERSON> } from "@/lib/resp";
import { useFetch } from "@/lib/use-fetch-api";
import { <PERSON>ert, Button, Card, CardBody, CardFooter, CardHeader, Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@heroui/react";
import { useEffect, useState } from "react";
import { TuiKeButton } from "./tui-ke-button";
import { CourseInfoButton } from "../../components/course-info-button";

export function ListXuanKeCard(props: {
    view: "all" | "student"
    canTuiKe?: boolean
}) {

    const { doRequest, data, error, loadState } = useFetch({
        url: props.view === "student" ? "/api/xuan-ke/get-by-student" : "/api/xuan-ke",
        method: "GET",
        loadFirst: true
    })

    const [xuankeList, setXuankeList] = useState<XuanKe[]>([])

    useEffect(() => {
        if (loadState !== "ok") return;
        if (data === null || data === undefined) return;
        setXuankeList(data)
    }, [loadState, data])

    return <Card>
        <CardHeader>
            {
                props.canTuiKe === true ? <h2>选课查询 / 退课请求</h2> : <h2>选课查询</h2>
            }

        </CardHeader>

        <CardBody className="flex flex-col gap-4">
            {
                loadState === "loading" && <p>加载中...</p>
            }

            {
                loadState === "error" && <Alert color={"danger"}>
                    <p>{error?.msg} ({error?.code})</p>
                </Alert>
            }

            <Table>
                <TableHeader>
                    <TableColumn>ID</TableColumn>
                    <TableColumn>学号</TableColumn>
                    <TableColumn>课程ID</TableColumn>
                    <TableColumn>分数</TableColumn>
                    <TableColumn>时间</TableColumn>
                    <TableColumn>退课申请</TableColumn>

                    <TableColumn>操作</TableColumn>
                </TableHeader>

                <TableBody>
                    {
                        xuankeList.map(xuanke => {
                            return <TableRow key={xuanke.ID}>
                                <TableCell>{xuanke.ID}</TableCell>

                                <TableCell>{xuanke.SID}</TableCell>

                                <TableCell>
                                    <CourseInfoButton courseId={xuanke.CID}>查看课程</CourseInfoButton>
                                </TableCell>

                                <TableCell>
                                    {xuanke.SCORE === null ? "未出分" : xuanke.SCORE}
                                </TableCell>

                                <TableCell>
                                    {
                                        new Date(xuanke.TIME * 1000).toLocaleString()
                                    }
                                </TableCell>

                                <TableCell>
                                    {xuanke.DEL_APP === null ? "未申请退课" : xuanke.DEL_APP}
                                </TableCell>

                                <TableCell>
                                    {
                                        props.canTuiKe === true ?
                                            <TuiKeButton id={xuanke.ID} onOk={doRequest} />
                                            : <span>无</span>
                                    }

                                </TableCell>

                            </TableRow>

                        })
                    }
                </TableBody>
            </Table>

        </CardBody>

        <CardFooter>
            <Button color={"primary"} onPress={doRequest}>刷新</Button>
        </CardFooter>
    </Card>
}