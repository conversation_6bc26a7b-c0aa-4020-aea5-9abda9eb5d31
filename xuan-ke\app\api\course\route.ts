import db from "@/lib/db";
import { MyResp } from "@/lib/resp";
import { NextRequest, NextResponse } from "next/server";

export async function GET(_request: NextRequest) {

    const res = await db.query(`SELECT ID,
        NAME,
        CLASSROOM,
        TEACHER,
        TIME,
        PRE_COURSE,
        CREDIT,
        NOTE
        FROM ke_cheng;`);

    const [rows] = res;

    const respJson: MyResp<any> = {
        code: "ok",
        msg: "ok",
        data: rows
    }

    return NextResponse.json(respJson);
}