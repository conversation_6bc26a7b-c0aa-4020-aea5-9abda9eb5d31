import Link from "next/link"

function MyLink({ children, href }: { children: React.ReactNode, href: string }) {
  return (
    <Link href={href} className="text-blue-500 hover:underline text-lg bg-white/50 p-4 shadow-lg rounded-lg border-1 border-gray-300">{children}</Link>
  )
}

export default function Home() {

  return (
    <div>
      <h1 className="text-3xl font-bold mb-4">欢迎使用学生选课系统</h1>
      <div className="flex flex-col gap-4">
        <MyLink href={"/student"}>我是学生</MyLink>
        <MyLink href={"/jiaowuchu"}>我是教务处管理员</MyLink>
        <MyLink href={"/xueyuan"}>我是学院管理员</MyLink>
      </div>
    </div>
  )
}