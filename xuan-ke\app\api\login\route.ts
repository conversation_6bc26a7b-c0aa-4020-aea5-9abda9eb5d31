import { createSession } from "@/lib/cookie";
import db from "@/lib/db";
import { MyR<PERSON><PERSON>, Student, User } from "@/lib/resp";
import { NextRequest, NextResponse } from "next/server";

async function doLogin(request: NextRequest): Promise<MyResp<any>> {

    const { username, password } = await request.json()

    console.log("login", username, password)

    let user: User | null = null
    let student: Student | null = null;


    let queryStudent = true;
    let userId = username
    do {
        // 查询用户表
        // console.log("query user", userId)
        const resp = await db.execute(`SELECT ID, PASSWORD, ROLE
        FROM user
        WHERE ID = ?;`, [userId]);

        const res = resp[0] as any[];

        if (res.length !== 0) {
            user = res[0] as User
            break;
        }

        if (queryStudent) {
            queryStudent = false // 只查一次

            // 从学生表里查询名字
            const resp = await db.execute(`
                SELECT ID, NAME, NOTE, SUSPENDED, REGISTERED
                FROM xue_sheng
                WHERE NAME = ?
                ORDER BY ID DESC;`, [username]);

            const res = resp[0] as any[];

            if (res.length === 1) {
                student = res[0] as Student

                console.log("student", student)

                if (!student.REGISTERED) {
                    return {
                        code: "student_not_registered",
                        msg: "学生未注册：" + username,
                        data: null
                    }
                }
                // 继续查用户表
                userId = String(student.ID)
                continue;
            } else if (res.length > 1) {
                return {
                    code: "student_not_unique",
                    msg: `存在${res.length}个名为${username}的学生，请使用学号登录`,
                    data: null
                }
            }
        }
        break;
    } while (true);

    if (user === null || user === undefined) {
        if (student !== null && student !== undefined) {
            return {
                code: "student_not_registered",
                msg: "学生未注册：" + username,
                data: {
                    "student": student
                }
            }
        }

        return {
            code: "user_not_found",
            msg: "不存在的用户：" + username,
            data: null
        }
    }

    // 比较密码是否一致
    if (user.PASSWORD !== password) {
        return {
            code: "password_wrong",
            msg: "密码错误",
            data: null
        }
    }

    // 加载学生
    if (student === null || student === undefined && user.ROLE === "学生") {
        const resp = await db.execute(`
            SELECT ID, NAME, NOTE, SUSPENDED, REGISTERED
            FROM xue_sheng
            WHERE ID = ?
            ORDER BY ID DESC;`, [user.ID]);

        const res = resp[0] as any[];

        if (res.length === 1) {
            student = res[0] as Student
        }
    }

    // 创建会话
    const sess = await createSession(user, student)

    // 隐藏密码
    user.PASSWORD = "***"

    return {
        code: "ok",
        msg: "ok",
        data: {
            "user": user,
            "student": student,
            "sid": sess.id
        }
    }
}


export async function POST(request: NextRequest) {
    // const { username, password } = await request.json()
    const r = await doLogin(request)
    return NextResponse.json(r)
}
