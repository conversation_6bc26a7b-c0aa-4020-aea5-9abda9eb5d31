{"name": "next-app-template", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint --fix"}, "dependencies": {"@heroui/button": "2.2.20", "@heroui/code": "2.2.15", "@heroui/input": "2.4.20", "@heroui/kbd": "2.2.16", "@heroui/link": "2.2.17", "@heroui/listbox": "2.3.19", "@heroui/navbar": "2.2.18", "@heroui/react": "^2.7.9", "@heroui/snippet": "2.2.21", "@heroui/switch": "2.2.18", "@heroui/system": "2.4.16", "@heroui/theme": "2.4.16", "@react-aria/ssr": "3.9.8", "@react-aria/visually-hidden": "3.8.23", "clsx": "2.1.1", "dotenv": "^16.5.0", "framer-motion": "11.13.1", "intl-messageformat": "10.7.16", "mysql2": "^3.14.1", "next": "15.3.1", "next-themes": "0.4.6", "react": "18.3.1", "react-dom": "18.3.1"}, "devDependencies": {"@eslint/compat": "1.2.8", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.25.1", "@next/eslint-plugin-next": "15.3.1", "@react-types/shared": "3.29.0", "@types/node": "22.15.3", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "8.31.1", "@typescript-eslint/parser": "8.31.1", "autoprefixer": "10.4.21", "eslint": "9.25.1", "eslint-config-next": "15.3.1", "eslint-config-prettier": "10.1.2", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "5.2.6", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-unused-imports": "4.1.4", "globals": "16.0.0", "postcss": "8.5.3", "prettier": "3.5.3", "tailwind-variants": "0.3.0", "tailwindcss": "3.4.16", "typescript": "5.6.3"}}