import { getSession } from "@/lib/cookie";
import db from "@/lib/db";
import { MyResp } from "@/lib/resp";
import { ResultSetHeader } from "mysql2";
import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {

    const session = await getSession();

    if (session === null) {
        const res: MyResp = {
            code: "no_login",
            msg: "未登录",
            data: null
        }
        return NextResponse.json(res);
    }

    if (session.user.ROLE !== "教务处") {
        const res: MyResp = {
            code: "no_permission",
            msg: "无权限，仅教务处",
            data: null
        }
        return NextResponse.json(res);
    }

    const { student_id } = await req.json();

    if (student_id === null || student_id === undefined || student_id === "") {
        const res: MyResp = {
            code: "no_student_id",
            msg: "未提供学号",
            data: null
        }
        return NextResponse.json(res);
    }

    // 随机密码
    const password = Math.random().toString(36).slice(-8);

    try {
        const [res] = await db.execute(`
        INSERT INTO user (ID, PASSWORD, ROLE)
        SELECT ID, ?, '学生'
        FROM xue_sheng
        WHERE ID = ?;`, [password, student_id]);

        const res2 = res as ResultSetHeader;

        if (res2.affectedRows === 0) {
            const res: MyResp = {
                code: "no_student",
                msg: "学号不存在",
                data: null
            }
            return NextResponse.json(res);
        }
    } catch (e) {
        const res: MyResp = {
            code: "db_error",
            msg: "数据库错误，可能已经注册: " + e,
            data: null
        }
        return NextResponse.json(res);
    }

    // 标记已经注册
    try {
        await db.execute(`
        UPDATE xue_sheng
        SET REGISTERED = true
        WHERE ID = ?;`, [student_id]);
    } catch (e) {
        const res: MyResp = {
            code: "db_error",
            msg: "数据库错误: " + e,
            data: null
        }
        return NextResponse.json(res);
    }

    const res: MyResp = {
        code: "ok",
        msg: "ok",
        data: {
            message: `学号 ${student_id} 注册成功，密码为 ${password}`
        }
    }

    return NextResponse.json(res);
}