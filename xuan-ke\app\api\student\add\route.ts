import { getSession } from "@/lib/cookie";
import db from "@/lib/db";
import { MyResp } from "@/lib/resp";
import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {

    const session = await getSession();

    if (session === null) {
        const resp: MyResp = {
            code: "no_login",
            msg: "未登录",
            data: null
        }
        return NextResponse.json(resp);
    }

    // 权限，只有学院
    if (session.user.ROLE !== "学院") {
        const resp: MyResp = {
            code: "no_permission",
            msg: "仅学院可操作",
            data: null
        }
        return NextResponse.json(resp);
    }

    const { name, id, note } = await req.json();

    // 检查参数
    if (name === null || name === undefined || name === "") {
        const resp: MyResp = {
            code: "invalid_name",
            msg: "姓名不能为空",
            data: null
        }
        return NextResponse.json(resp);
    }

    if (id === null || id === undefined || id === "") {
        const resp: MyResp = {
            code: "invalid_id",
            msg: "学号不能为空",
            data: null
        }
        return NextResponse.json(resp);
    }

    if (note === null || note === undefined || note === "") {
        const resp: MyResp = {
            code: "invalid_note",
            msg: "备注不能为空",
            data: null
        }
        return NextResponse.json(resp);
    }

    // 数据库操作
    try {
        const res = await db.execute(`
        INSERT INTO xue_sheng (ID, NAME, NOTE, SUSPENDED, REGISTERED)
        VALUES (?, ?, ?, ?, ?);`, [id, name, note, 0, 0]);

    } catch (e) {
        console.log(e);
        const resp: MyResp = {
            code: "db_error",
            msg: "数据库错误: " + String(e),
            data: null
        }
        return NextResponse.json(resp);
    }

    // console.log(name, id, note);

    const resp: MyResp = {
        code: "ok",
        msg: "ok",
        data: null
    }
    return NextResponse.json(resp);
}