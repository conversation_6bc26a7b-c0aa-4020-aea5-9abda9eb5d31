import { getSession } from "@/lib/cookie";
import db from "@/lib/db";
import { MyResp } from "@/lib/resp";
import { ResultSetHeader } from "mysql2";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {

    // 参数
    const { course_id, student_id } = await request.json();

    const session = await getSession();

    if (session === null) {
        const r: MyResp = {
            code: "no_login",
            msg: "未登录",
            data: null
        }
        return NextResponse.json(r);
    }

    if (session.user.ROLE !== "教务处") {
        const r: MyResp = {
            code: "no_permission",
            msg: "无权限，仅教务处",
            data: null
        }
        return NextResponse.json(r);
    }

    if (course_id === undefined || course_id === null || course_id === "") {
        const r: MyResp = {
            code: "no_course_id",
            msg: "课程ID为空",
            data: null
        }
        return NextResponse.json(r);
    }

    if (student_id === undefined || student_id === null || student_id === "") {
        const r: MyResp = {
            code: "no_student_id",
            msg: "学生ID为空",
            data: null
        }
        return NextResponse.json(r);
    }

    try {
        const [res] = await db.execute(`
        UPDATE xuan_ke
        SET DEL_APP = '已批准'
        WHERE DEL_APP = '已提交'
        AND CID = ?
        AND SID = ?;`, [course_id, student_id]);

        const r = res as ResultSetHeader

        if (r.affectedRows === 0) {
            const r: MyResp = {
                code: "no_record",
                msg: "没有找到记录，学生未选课或未提交退课申请或已同意退课",
                data: null
            }
            return NextResponse.json(r);
        }

    } catch (e) {
        const r: MyResp = {
            code: "db_error",
            msg: "数据库错误：" + e,
            data: null
        }
        return NextResponse.json(r);
    }

    const r: MyResp = {
        code: "ok",
        msg: "退课申请批准成功",
        data: null
    }
    return NextResponse.json(r);
}