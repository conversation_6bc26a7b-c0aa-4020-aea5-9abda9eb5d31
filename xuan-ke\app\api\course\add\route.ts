import { getSession } from "@/lib/cookie";
import db from "@/lib/db";
import { MyResp } from "@/lib/resp";
import { NextRequest, NextResponse } from "next/server";


export async function POST(request: NextRequest) {
    // 权限检查
    const sess = await getSession();

    if (sess === null) {
        const resp: MyResp = {
            code: "no_login",
            msg: "请先登录",
            data: null
        }
        return NextResponse.json(resp);
    }

    if (sess.user.ROLE !== "教务处") {
        const resp: MyResp = {
            code: "no_permission",
            msg: "仅教务处可添加课程",
            data: null
        }
        return NextResponse.json(resp);
    }

    const body = await request.json();

    /**
     * <Input name={"id"} label={"课程编号"}></Input>
                <Input name={"name"} label={"课程名称"}></Input>
                <Input name={"teacher"} label={"授课教师"}></Input>
                <Input name={"credit"} label={"学分"}></Input>

                <Input name={"time"} label={"上课时间"}></Input>
                <Input name={"location"} label={"上课地点"}></Input>
                <Input name={"description"} label={"课程说明"}></Input>
                <Input name={"pre_course"} label={"先修课程"}></Input>
     */

    let { id, name, teacher, credit,
        time, location, description, pre_course } = body;

    // 检查参数
    if (id === null || id === undefined || id === "") {
        const resp: MyResp = {
            code: "bad_request",
            msg: "课程编号不能为空",
            data: null
        }
        return NextResponse.json(resp);
    }

    if (name === null || name === undefined || name === "") {
        const resp: MyResp = {
            code: "bad_request",
            msg: "课程名称不能为空",
            data: null
        }
        return NextResponse.json(resp);
    }

    if (teacher === null || teacher === undefined || teacher === "") {
        const resp: MyResp = {
            code: "bad_request",
            msg: "授课教师不能为空",
            data: null
        }
        return NextResponse.json(resp);
    }

    if (credit === null || credit === undefined || credit === "") {
        const resp: MyResp = {
            code: "bad_request",
            msg: "学分不能为空",
            data: null
        }
        return NextResponse.json(resp);
    }

    if (time === null || time === undefined || time === "") {
        const resp: MyResp = {
            code: "bad_request",
            msg: "上课时间不能为空",
            data: null
        }
        return NextResponse.json(resp);
    }

    if (location === null || location === undefined || location === "") {
        const resp: MyResp = {
            code: "bad_request",
            msg: "上课地点不能为空",
            data: null
        }
    }

    if (description === null || description === undefined || description === "") {
        const resp: MyResp = {
            code: "bad_request",
            msg: "课程说明不能为空",
            data: null
        }
        return NextResponse.json(resp);
    }

    if (pre_course === null || pre_course === undefined || pre_course === "") {
        pre_course = null
    }

    // 数据库操作
    try {
        const res = await db.execute(`
        INSERT INTO ke_cheng (ID, NAME, CLASSROOM, TEACHER, TIME, PRE_COURSE, CREDIT, NOTE)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?);`, [id, name, location, teacher, time, pre_course, credit, description])
    } catch (e) {
        console.error(e);
        const r: MyResp = {
            code: "db_error",
            msg: "数据库错误，可能编号或名称重复: " + e,
            data: null
        }
        return NextResponse.json(r);
    }

    const resp: MyResp = {
        code: "ok",
        msg: "添加成功",
        data: null
    }
    return NextResponse.json(resp);
}