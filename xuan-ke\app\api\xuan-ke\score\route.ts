import { getSession } from "@/lib/cookie";
import db from "@/lib/db";
import { MyResp } from "@/lib/resp";
import { ResultSetHeader } from "mysql2";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {

    const { course_id, student_id, score } = await request.json();

    const session = await getSession();

    if (session === null) {
        const r: MyResp = {
            code: "no_login",
            msg: "未登录",
            data: null
        }
        return NextResponse.json(r)
    }

    if (session.user.ROLE !== "学院") {
        const r: MyResp = {
            code: "no_permission",
            msg: "无权限，仅学院可录入成绩",
            data: null
        }
        return NextResponse.json(r)
    }

    // 检查成绩是否合法
    if (student_id === null || student_id === undefined || student_id === "") {
        const r: MyResp = {
            code: "no_student_id",
            msg: "未提供学生学号",
            data: null
        }
        return NextResponse.json(r)
    }

    if (course_id === null || course_id === undefined || course_id === "") {
        const r: MyResp = {
            code: "no_course_id",
            msg: "未提供课程号",
            data: null
        }
        return NextResponse.json(r)
    }

    if (score === null || score === undefined || score === "") {
        const r: MyResp = {
            code: "no_score",
            msg: "未提供成绩",
            data: null
        }
        return NextResponse.json(r)
    }

    try {
        const [res] = await db.execute(`UPDATE xuan_ke
            SET SCORE = ?
            WHERE SID = ?
            AND CID = ? AND SCORE IS NULL;`, [score, student_id, course_id])

        const r = res as ResultSetHeader

        if (r.affectedRows === 0) {
            const r2: MyResp = {
                code: "no_record",
                msg: "未找到该学生选课记录或已录入成绩",
                data: null
            }
            return NextResponse.json(r2)
        }
    } catch (e) {
        const r: MyResp = {
            code: "db_error",
            msg: "数据库错误: " + e,
            data: null
        }
        return NextResponse.json(r)
    }

    const r2: MyResp = {
        code: "ok",
        msg: "成绩录入成功",
        data: null
    }

    return NextResponse.json(r2)
}