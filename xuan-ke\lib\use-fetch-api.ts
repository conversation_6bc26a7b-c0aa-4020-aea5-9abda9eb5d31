"use client"

import { useCallback, useEffect, useRef, useState } from "react";
import { MyResp } from "./resp";

export type LoadState = 'loading' | 'ok' | 'error'

export function useFetch(params: {
    url: string,
    method: string,
    body?: any,
    loadFirst?: boolean,
}) {

    const [reqCount, setReqCount] = useState(0)

    const [loadState, setLoadState] = useState<LoadState | undefined>()
    const [data, setData] = useState<any>()
    const [error, setError] = useState<MyResp<any>>()

    const urlRef = useRef(params.url)
    const methodRef = useRef(params.method)
    const bodyRef = useRef(params.body)

    urlRef.current = params.url
    methodRef.current = params.method
    bodyRef.current = params.body

    const doRequest = useCallback(() => {
        setReqCount(v => v + 1)
    }, [])

    useEffect(() => {
        if (reqCount === 0) return;

        let abort: AbortController | null = null

        const doIt = async () => {
            let res: Response
            abort = new AbortController()
            try {
                setLoadState('loading')

                let body = undefined
                if (bodyRef.current) {
                    body = JSON.stringify(bodyRef.current)
                }

                res = await fetch(urlRef.current, {
                    method: methodRef.current,
                    body: body,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    signal: abort.signal
                })
            } catch (e) {
                setLoadState('error')
                setError({ code: 'network', msg: String(e), data: e })
                return
            }

            // 响应状态码
            if (res.status !== 200) {
                setLoadState('error')
                setError({ code: 'http', msg: `http: ${res.statusText} (${res.status})`, data: null })
                return
            }

            // 文本
            let text;
            try {
                text = await res.text()
            } catch (e) {
                setLoadState('error')
                setError({ code: 'network', msg: String(e), data: e })
                return
            }

            // 解析JSON
            let json: any
            try {
                json = JSON.parse(text)
            } catch (e) {
                setLoadState('error')
                setError({ code: 'resp_not_json', msg: String(e), data: e })
                return
            }

            const err = json as MyResp<any>
            if (err.code !== "ok") {
                setLoadState('error')
                setError(err)
                return
            }

            setLoadState('ok')
            setData(err.data)
        }

        const t = setTimeout(() => {
            doIt()
        })

        return () => {
            if (abort) abort.abort()

            clearTimeout(t)
        }
    }, [reqCount])

    useEffect(() => {
        if (params.loadFirst === true) {
            doRequest()
        }
    }, [params.loadFirst])

    return {
        loadState,
        data,
        error,
        doRequest,
    }
}