"use client"

import { Course } from "@/lib/resp"
import { useFetch } from "@/lib/use-fetch-api"
import { But<PERSON> } from "@heroui/button"
import { Mo<PERSON>, ModalContent, ModalHeader, ModalBody } from "@heroui/react"
import { useState } from "react"

export function CourseInfoButton(props: {
    courseId: number | string
    children: React.ReactNode
}) {

    const { data, error, loadState } = useFetch({
        url: "/api/course/id?id=" + props.courseId,
        method: "GET",
        loadFirst: true,
    })

    const course = data as Course | null | undefined;

    const [show, setShow] = useState(false);

    return <>
        <Modal isOpen={show} onClose={() => setShow(false)}>
            <ModalContent>
                <ModalHeader><h2>课程信息</h2></ModalHeader>
                <ModalBody>
                    {loadState === "loading" && <p>加载中...</p>}

                    {
                        course && loadState === "ok" ?
                            <>
                                <p>编号：{course.ID}</p>
                                <p>名称: {course.NAME}</p>
                                <p>教师：{course.TEACHER}</p>
                                <p>教室：{course.CLASSROOM}</p>

                                <p>描述: {course.NOTE}</p>
                                <p>学分: {course.CREDIT}</p>
                                <p>上课时间: {course.TIME}</p>
                                <p>先修课程: {course.PRE_COURSE}</p>
                            </> : <></>
                    }

                    {
                        loadState === "error" && <p>{`${error?.msg} (${error?.code})`}</p>
                    }
                </ModalBody>
            </ModalContent>
        </Modal>

        <Button onPress={() => setShow(true)}>
            {props.children}
        </Button>
    </>
}