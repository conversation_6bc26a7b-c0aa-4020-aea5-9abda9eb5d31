import { getSession } from "@/lib/cookie";
import { MyResp } from "@/lib/resp";
import { sessionManager } from "@/lib/session";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

export async function POST(_request: NextRequest) {

    // 删除Cookie
    const session = await getSession()

    if (session === null) {
        const resp: MyResp = {
            code: "no_login",
            msg: "未登录",
            data: null,
        }
        return NextResponse.json(resp);
    }

    // 删除Cookie
    const cookieStore = await cookies();
    cookieStore.delete("sid")

    // 删除会话

    sessionManager.removeSession(session.id)

    const resp: MyResp = {
        code: "ok",
        msg: "登出成功",
        data: null,
    }

    return NextResponse.json(resp);
}