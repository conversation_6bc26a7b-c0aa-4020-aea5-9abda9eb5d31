"use client"

import { Student } from "@/lib/resp";
import { useFetch } from "@/lib/use-fetch-api";
import { Alert, Card, CardBody, CardHeader, Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@heroui/react";
import { useEffect, useState } from "react";

export function StudentListCard() {

    const { data, error, doRequest, loadState } = useFetch({
        url: "/api/student",
        method: "GET",
        loadFirst: true,
    })

    const [stus, setStus] = useState<Student[]>([])
    const [showError, setShowError] = useState(false)

    // 成功
    useEffect(() => {
        if (loadState !== "ok") return;
        if (data === null || data === undefined) return;
        setStus(data as Student[])
    }, [loadState, data])

    // 错误
    useEffect(() => {
        if (loadState !== "error") return;
        if (error === null || error === undefined) return;
        setShowError(true)
    }, [loadState, error])

    return <Card>
        <CardHeader>学生列表</CardHeader>
        <CardBody>
            {loadState === "loading" && <div>加载中...</div>}

            <Alert color={"danger"} isVisible={showError} onClose={() => setShowError(false)}>
                {`${error?.msg} (${error?.code})`}
            </Alert>

            <Table>
                <TableHeader>
                    <TableColumn>ID</TableColumn>
                    <TableColumn>姓名</TableColumn>
                    <TableColumn>备注</TableColumn>
                    <TableColumn>是否休学</TableColumn>
                    <TableColumn>是否注册</TableColumn>
                </TableHeader>
                <TableBody>
                    {
                        stus.map(stu => {
                            return <TableRow key={stu.ID}>
                                <TableCell>{stu.ID}</TableCell>
                                <TableCell>{stu.NAME}</TableCell>
                                <TableCell>{stu.NOTE}</TableCell>
                                <TableCell>{stu.SUSPENDED ? "√" : "×"}</TableCell>
                                <TableCell>{stu.REGISTERED ? "是" : "否"}</TableCell>
                            </TableRow>
                        })
                    }

                </TableBody>
            </Table>
        </CardBody>
    </Card>
}