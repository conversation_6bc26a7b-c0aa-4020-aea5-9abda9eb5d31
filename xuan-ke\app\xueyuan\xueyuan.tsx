"use client"

import { Tab, Tabs } from "@heroui/react"
import { LuRuCard } from "./lu-ru-card"
import { StudentListCard } from "./student-list"
import { LogoutButton } from "@/components/logout-button"
import { ChangePasswordCard } from "@/components/change-password-card"
import { ListXuanKeCard } from "../student/list-xuan-ke-card"
import { ListCourseCard } from "../jiaowuchu/list-course-card"
import { LuRuScore } from "./lu-ru-score"

export function XueYuanPage(props: {
    id: string,
    role: string
}) {
    // 学生信息录入
    // 选课查询
    // 课程信息查询
    // 学生信息查询
    // 成绩录入
    // 密码修改
    return (
        <>
            <h1 className="text-2xl font-bold mb-4">选课系统 | 学院</h1>
            <p className="mb-4">用户: {`${props.id} (${props.role})`}</p>
            <Tabs>
                <Tab title={"学生信息录入"}>
                    <LuRuCard />
                </Tab>

                <Tab title={"选课查询"}>
                    <ListXuanKeCard view={"all"} canTuiKe={false} />
                </Tab>

                <Tab title={"课程信息查询"}>
                    <ListCourseCard />
                </Tab>

                <Tab title={"学生信息查询"}>
                    <StudentListCard />
                </Tab>

                <Tab title={"成绩录入"}>
                    <LuRuScore />
                </Tab>

                <Tab title={"密码修改"}>
                    <ChangePasswordCard />
                </Tab>

                <Tab title={"退出"}>
                    <LogoutButton />
                </Tab>
            </Tabs>
        </>
    )


}