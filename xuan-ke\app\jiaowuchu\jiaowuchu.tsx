"use client"

import { LogoutButton } from "@/components/logout-button"
import { Tab, Tabs } from "@heroui/react"
import { AddTab } from "./add-tab";
import { TuiXueCard } from "./tui-xue-card";
import { ZhuCeCard } from "./zhu-ce";
import { ChangePasswordCard } from "@/components/change-password-card";
import { TuiKeChuLiCard } from "./tui-ke-chu-li-card";
import { XiuFuXueCard } from "./xiu-fu-xue-card";


export default function JiaowuchuPage(props: {
    id: string;
    role: string
}) {
    // 课程录入
    // 退学处理
    // 退课处理
    // 修复学处理
    // 注册处理
    // 密码修改

    return <>
        <h1 className="text-3xl font-bold mb-4">选课系统 | 教务处</h1>
        <p className="mb-4">{props.id} ({props.role})</p>
        <Tabs>
            <Tab title={"课程录入"}>
                <AddTab />
            </Tab>

            <Tab title={"退学处理"}>
                <TuiXueCard />
            </Tab>
            <Tab title={"退课处理"}>
                <TuiKeChuLiCard />
            </Tab>
            <Tab title={"修复学处理"}>
                <XiuFuXueCard />
            </Tab>
            <Tab title={"注册处理"}>
                <ZhuCeCard />
            </Tab>

            <Tab title={"密码修改"}>
                <ChangePasswordCard />
            </Tab>

            <Tab title={"退出"}>
                <LogoutButton />
            </Tab>
        </Tabs>

    </>

}