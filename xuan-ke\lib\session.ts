import { Student, User } from "./resp";

export class Session {
    public readonly id: string;
    public readonly user: User;
    public readonly student: Student | null;

    constructor(id: string, user: User, student: Student | null) {
        this.id = id;
        this.user = user;
        this.student = student;
    }

    public static create(user: User, student: Student | null): Session {
        // 随机UUID
        const id = crypto.randomUUID();
        return new Session(id, user, student);
    }
}

export class SessionManager {

    private readonly map: Map<string, Session>;

    constructor() {
        this.map = new Map<string, Session>();
    }

    public addSession(session: Session) {
        this.map.set(session.id, session);
        return session;
    }

    public removeSession(id: string) {
        this.map.delete(id);
    }

    public getSession(id: string): Session | null {
        const s = this.map.get(id);
        if (s) return s;
        return null;
    }
}


const globalForSessionManager = global as unknown as { sessionManager: SessionManager };

export const sessionManager = globalForSessionManager.sessionManager || new SessionManager();

if (process.env.NODE_ENV !== "production") globalForSessionManager.sessionManager = sessionManager;