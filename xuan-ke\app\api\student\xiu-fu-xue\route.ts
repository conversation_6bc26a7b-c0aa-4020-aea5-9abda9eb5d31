import { getSession } from "@/lib/cookie";
import db from "@/lib/db";
import { MyResp } from "@/lib/resp";
import { ResultSetHeader } from "mysql2/promise";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
    const data = await request.json()
    const { student_id, operation } = data

    if (student_id === null || student_id === undefined || student_id === "") {
        const res: MyResp = {
            code: "invalid_student_id",
            msg: "student_id不能为空",
            data: null
        }
        return NextResponse.json(res)
    }

    // 权限检查
    const session = await getSession();

    if (session === null || session.user.ROLE !== "教务处") {
        const res: MyResp = {
            code: "no_permission",
            msg: "仅教务处可登记退学",
            data: null
        }
        return NextResponse.json(res)
    }

    let SUSPENDED: boolean;

    if (operation === "xiu-xue") {
        SUSPENDED = true
    } else if (operation === "fu-xue") {
        SUSPENDED = false
    } else {
        const r: MyResp = {
            code: "invalid_operation",
            msg: "operation必须是'xiu-xue'或'fu-xue'",
            data: null
        }
        return NextResponse.json(r)
    }

    try {
        const [res] = await db.execute(`
            UPDATE xue_sheng
            SET SUSPENDED = ?
            WHERE ID = ?;`, [SUSPENDED, student_id])


        // 获取影响的行数
        const affectedRows = (res as ResultSetHeader).affectedRows;

        if (affectedRows === 0) {
            const res: MyResp = {
                code: "no_student",
                msg: "没有找到该学生",
                data: null
            }
            return NextResponse.json(res)
        }
    } catch (e) {
        const res: MyResp = {
            code: "db_error",
            msg: "数据库错误",
            data: null
        }
        return NextResponse.json(res)
    }


    const res: MyResp = {
        code: "ok",
        msg: "操作成功",
        data: null
    }

    return NextResponse.json(res)
}