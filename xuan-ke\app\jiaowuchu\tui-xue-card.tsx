"use client";

import { useFetch } from "@/lib/use-fetch-api";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, CardBody, CardHeader, Input } from "@heroui/react";
import { useEffect, useState } from "react";

export function TuiXueCard() {

    const [studentID, setStudentID] = useState("")

    const { data, error, loadState, doRequest } = useFetch({
        url: "/api/student/tui-xue",
        method: "POST",
        body: {
            student_id: studentID
        },
        loadFirst: false
    })

    const [showError, setShowError] = useState(false)
    const [showOk, setShowOk] = useState(false);

    useEffect(() => {
        if (loadState === "error") {
            setShowError(true)
        }
    }, [loadState])

    useEffect(() => {
        if (loadState === "ok") {
            setShowOk(true)
        }
    }, [loadState])

    const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault()
        const data = new FormData(e.currentTarget)
        const data2 = Object.fromEntries(data.entries())
        setStudentID(data2["student_id"] as string)
        doRequest()
    }

    return <Card>
        <CardHeader><h2>退学处理</h2></CardHeader>
        <CardBody>
            <form onSubmit={onSubmit} className="flex flex-col gap-4" onFocus={() => {
                setShowError(false); setShowOk(false)
            }}>
                <Input label={"学生学号"} name="student_id"
                    placeholder="请输入学生学号"
                ></Input>

                <Alert color={"danger"} isVisible={showError} onClose={() => setShowError(false)}>
                    {`${error?.msg} (${error?.code})`}
                </Alert>

                <Alert color={"success"} isVisible={showOk} onClose={() => setShowOk(false)}>
                    操作成功
                </Alert>

                <Button color={"primary"} type={"submit"} isLoading={loadState === "loading"}>确认</Button>
            </form>
        </CardBody>
    </Card>
}