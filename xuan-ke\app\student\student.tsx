"use client"

import { LogoutButton } from "@/components/logout-button";
import { Tabs, Tab } from "@heroui/react";
import { XuanKeCard } from "./xuan-ke-card";
import { ListXuanKeCard } from "./list-xuan-ke-card";
import { ListCourseCard } from "../jiaowuchu/list-course-card";
import { ChangePasswordCard } from "@/components/change-password-card";

export function StudentPage(props: {
    name?: string
}) {

    return (
        <>
            <h1 className="text-2xl font-bold mb-4">学生选课 | 学生</h1>
            <p className="text-gray-500 mb-4">欢迎 {props.name} 同学选课</p>
            <Tabs>
                <Tab title={"选课"}>
                    <div className="flex flex-col gap-4">
                        <ListCourseCard />
                        <XuanKeCard></XuanKeCard>
                    </div>

                </Tab>
                <Tab title={"选课查询"}>
                    <ListXuanKeCard view={"student"} canTuiKe={true} />
                </Tab>

                <Tab title={"密码修改"}>
                    <ChangePasswordCard />
                </Tab>

                <Tab title={"退出登录"}>
                    <LogoutButton />
                </Tab>
            </Tabs>
        </>
    )
}