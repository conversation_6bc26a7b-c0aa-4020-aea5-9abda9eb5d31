import db from "@/lib/db";
import { MyResp } from "@/lib/resp";
import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {

    try {
        const [res] = await db.query(`SELECT ID, SID, CID, TIME, SCORE, DEL_APP
            FROM xuan_ke
            ORDER BY ID DESC;`)

        const r: MyResp = { code: "ok", msg: '成功', data: res }
        return NextResponse.json(r);
    } catch (e) {
        console.log(e)
        const r: MyResp = { code: "db_error", msg: '数据库错误: ' + e, data: null }
        return NextResponse.json(r);
    }
}